#!/usr/bin/env python3
"""
Test script to check available Google AI models
"""

import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_models():
    """Test available Google AI models"""
    try:
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if not google_api_key or google_api_key == 'your_google_ai_api_key_here':
            print("❌ Google API key not configured")
            return
        
        genai.configure(api_key=google_api_key)
        
        print("🔍 Available Google AI models:")
        print("=" * 50)
        
        # List available models
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"✅ {model.name}")
                print(f"   Display Name: {model.display_name}")
                print(f"   Description: {model.description}")
                print()
        
        # Test the model from .env
        model_name = os.getenv('LLM_MODEL', 'gemini-1.5-flash')
        print(f"🧪 Testing model: {model_name}")
        print("=" * 50)
        
        try:
            model = genai.GenerativeModel(model_name)
            response = model.generate_content("Say 'Hello from " + model_name + "!'")
            print(f"✅ Model {model_name} is working!")
            print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Model {model_name} failed: {e}")
            
            # Try with gemini-1.5-flash as fallback
            print("\n🔄 Trying fallback model: gemini-1.5-flash")
            try:
                model = genai.GenerativeModel('gemini-1.5-flash')
                response = model.generate_content("Say 'Hello from gemini-1.5-flash!'")
                print("✅ Fallback model gemini-1.5-flash is working!")
                print(f"Response: {response.text}")
                print("\n💡 Suggestion: Update your .env file to use 'gemini-1.5-flash'")
            except Exception as e2:
                print(f"❌ Fallback model also failed: {e2}")
        
    except Exception as e:
        print(f"❌ Error testing models: {e}")

if __name__ == "__main__":
    test_models()
